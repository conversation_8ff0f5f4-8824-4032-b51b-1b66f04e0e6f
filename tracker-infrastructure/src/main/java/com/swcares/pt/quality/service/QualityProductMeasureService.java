package com.swcares.pt.quality.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;
import com.swcares.pt.quality.vo.QualityTrendVO;
import com.swcares.pt.quality.vo.QualityStatVO;

/**
 * ClassName：com.swcares.pt.quality.service.QualityProductMeasureService <br>
 * Description：产品质量度量 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
public interface QualityProductMeasureService extends BaseService<QualityProductMeasureDO> {

    /**
     * 分页查询产品质量度量记录
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<QualityProductMeasureVO> page(QualityProductMeasurePagedDTO dto);

    /**
     * 根据条件查询产品质量度量记录
     * @param params 查询条件
     * @return 质量度量记录列表
     */
    List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params);

    /**
     * 查询质量趋势数据
     * @param projectCode 项目编码
     * @param productName 产品名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 质量趋势数据
     */
    List<QualityTrendVO> getQualityTrend(String projectCode, String productName, String startDate, String endDate);

    /**
     * 查询质量统计数据
     * @param projectCode 项目编码
     * @param measureCycle 度量周期
     * @return 质量统计数据
     */
    List<QualityStatVO> getQualityStat(String projectCode, Integer measureCycle);

    /**
     * 查询最新的质量度量记录
     * @param projectCode 项目编码
     * @param productName 产品名称
     * @return 最新质量度量记录
     */
    QualityProductMeasureVO getLatestMeasure(String projectCode, String productName);

    /**
     * 根据项目编码删除质量度量记录
     * @param projectCode 项目编码
     * @return 是否删除成功
     */
    boolean deleteByProjectCode(String projectCode);

}
