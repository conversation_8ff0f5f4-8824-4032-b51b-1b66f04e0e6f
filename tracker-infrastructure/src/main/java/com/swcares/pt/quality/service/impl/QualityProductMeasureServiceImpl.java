package com.swcares.pt.quality.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.mapper.QualityProductMeasureMapper;
import com.swcares.pt.quality.service.QualityProductMeasureService;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;
import com.swcares.pt.quality.vo.QualityTrendVO;
import com.swcares.pt.quality.vo.QualityStatVO;

/**
 * ClassName：com.swcares.pt.quality.service.impl.QualityProductMeasureServiceImpl <br>
 * Description：产品质量度量 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Service
public class QualityProductMeasureServiceImpl extends BaseServiceImpl<QualityProductMeasureMapper, QualityProductMeasureDO> 
        implements QualityProductMeasureService {

    @Override
    public IPage<QualityProductMeasureVO> page(QualityProductMeasurePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params) {
        return baseMapper.selectByCondition(params);
    }

    @Override
    public List<QualityTrendVO> getQualityTrend(String projectCode, String productName, String startDate, String endDate) {
        return baseMapper.selectQualityTrend(projectCode, productName, startDate, endDate);
    }

    @Override
    public List<QualityStatVO> getQualityStat(String projectCode, Integer measureCycle) {
        return baseMapper.selectQualityStat(projectCode, measureCycle);
    }

    @Override
    public QualityProductMeasureVO getLatestMeasure(String projectCode, String productName) {
        return baseMapper.selectLatestMeasure(projectCode, productName);
    }

    @Override
    public boolean deleteByProjectCode(String projectCode) {
        return baseMapper.deleteByProjectCode(projectCode) > 0;
    }

}
