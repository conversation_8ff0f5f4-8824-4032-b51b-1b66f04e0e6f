package com.swcares.pt.quality.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;
import com.swcares.pt.quality.vo.QualityTrendVO;
import com.swcares.pt.quality.vo.QualityStatVO;

/**
 * ClassName：com.swcares.pt.quality.mapper.QualityProductMeasureMapper <br>
 * Description：产品质量度量 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
public interface QualityProductMeasureMapper extends BaseMapper<QualityProductMeasureDO> {

    /**
     * 分页查询产品质量度量记录
     * @param dto 查询条件
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<QualityProductMeasureVO> page(@Param("p") QualityProductMeasurePagedDTO dto, Page<QualityProductMeasureVO> page);

    /**
     * 根据条件查询产品质量度量记录
     * @param params 查询条件
     * @return 质量度量记录列表
     */
    List<QualityProductMeasureVO> selectByCondition(@Param("p") QualityProductMeasureDTO params);

    /**
     * 查询质量趋势数据
     * @param projectCode 项目编码
     * @param productName 产品名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 质量趋势数据
     */
    List<QualityTrendVO> selectQualityTrend(@Param("projectCode") String projectCode, 
                                           @Param("productName") String productName,
                                           @Param("startDate") String startDate, 
                                           @Param("endDate") String endDate);

    /**
     * 查询质量统计数据
     * @param projectCode 项目编码
     * @param measureCycle 度量周期
     * @return 质量统计数据
     */
    List<QualityStatVO> selectQualityStat(@Param("projectCode") String projectCode, 
                                         @Param("measureCycle") Integer measureCycle);

    /**
     * 查询最新的质量度量记录
     * @param projectCode 项目编码
     * @param productName 产品名称
     * @return 最新质量度量记录
     */
    QualityProductMeasureVO selectLatestMeasure(@Param("projectCode") String projectCode, 
                                               @Param("productName") String productName);

    /**
     * 根据项目编码删除质量度量记录
     * @param projectCode 项目编码
     * @return 删除记录数
     */
    int deleteByProjectCode(@Param("projectCode") String projectCode);

}
