package com.swcares.pt.quality.entity;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.mybatis.base.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.quality.entity.QualityProductMeasureDO <br>
 * Description：产品质量度量 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("quality_product_measure")
public class QualityProductMeasureDO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 项目编码 */
    private String projectCode;

    /** 产品名称 */
    private String productName;

    /** 产品版本 */
    private String productVersion;

    /** 度量日期 */
    private LocalDate measureDate;

    /** 度量周期(1-日,2-周,3-月,4-季度,5-年) */
    private Integer measureCycle;

    /** 代码行数 */
    private Long codeLines;

    /** 注释行数 */
    private Long commentLines;

    /** 空行数 */
    private Long blankLines;

    /** 总行数 */
    private Long totalLines;

    /** 注释率(%) */
    private BigDecimal commentRate;

    /** 代码复杂度 */
    private BigDecimal complexity;

    /** 代码重复率(%) */
    private BigDecimal duplicationRate;

    /** 技术债务(小时) */
    private BigDecimal technicalDebt;

    /** 可维护性评级(A-E) */
    private String maintainabilityRating;

    /** 可靠性评级(A-E) */
    private String reliabilityRating;

    /** 安全性评级(A-E) */
    private String securityRating;

    /** 单元测试覆盖率(%) */
    private BigDecimal testCoverage;

    /** 单元测试数量 */
    private Integer testCount;

    /** 单元测试通过数量 */
    private Integer testPassCount;

    /** 单元测试失败数量 */
    private Integer testFailCount;

    /** 缺陷总数 */
    private Integer bugCount;

    /** 严重缺陷数 */
    private Integer criticalBugCount;

    /** 主要缺陷数 */
    private Integer majorBugCount;

    /** 次要缺陷数 */
    private Integer minorBugCount;

    /** 缺陷密度(缺陷数/千行代码) */
    private BigDecimal bugDensity;

    /** 代码审查覆盖率(%) */
    private BigDecimal reviewCoverage;

    /** 代码审查发现问题数 */
    private Integer reviewIssueCount;

    /** 性能评分 */
    private BigDecimal performanceScore;

    /** 响应时间(ms) */
    private BigDecimal responseTime;

    /** 吞吐量(TPS) */
    private BigDecimal throughput;

    /** 内存使用率(%) */
    private BigDecimal memoryUsage;

    /** CPU使用率(%) */
    private BigDecimal cpuUsage;

    /** 质量门禁状态(1-通过,0-未通过) */
    private Integer qualityGateStatus;

    /** 质量评分 */
    private BigDecimal qualityScore;

    /** 度量工具 */
    private String measureTool;

    /** 度量人员 */
    private String measureBy;

    /** 备注 */
    private String remark;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

}
