package com.swcares.pt.quality.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.pt.quality.vo.QualityTrendVO <br>
 * Description：质量趋势 视图对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="QualityTrendVO", description="质量趋势视图")
public class QualityTrendVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "度量日期")
    private LocalDate measureDate;

    @ApiModelProperty(value = "质量评分")
    private BigDecimal qualityScore;

    @ApiModelProperty(value = "代码行数")
    private Long codeLines;

    @ApiModelProperty(value = "注释率(%)")
    private BigDecimal commentRate;

    @ApiModelProperty(value = "代码复杂度")
    private BigDecimal complexity;

    @ApiModelProperty(value = "代码重复率(%)")
    private BigDecimal duplicationRate;

    @ApiModelProperty(value = "技术债务(小时)")
    private BigDecimal technicalDebt;

    @ApiModelProperty(value = "单元测试覆盖率(%)")
    private BigDecimal testCoverage;

    @ApiModelProperty(value = "缺陷总数")
    private Integer bugCount;

    @ApiModelProperty(value = "缺陷密度(缺陷数/千行代码)")
    private BigDecimal bugDensity;

    @ApiModelProperty(value = "代码审查覆盖率(%)")
    private BigDecimal reviewCoverage;

    @ApiModelProperty(value = "性能评分")
    private BigDecimal performanceScore;

    @ApiModelProperty(value = "质量门禁状态(1-通过,0-未通过)")
    private Integer qualityGateStatus;

}
