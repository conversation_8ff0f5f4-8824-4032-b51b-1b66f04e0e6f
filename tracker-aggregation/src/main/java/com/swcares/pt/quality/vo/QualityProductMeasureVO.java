package com.swcares.pt.quality.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.pt.quality.vo.QualityProductMeasureVO <br>
 * Description：产品质量度量 视图对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="QualityProductMeasureVO", description="产品质量度量视图")
public class QualityProductMeasureVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品版本")
    private String productVersion;

    @ApiModelProperty(value = "度量日期")
    private LocalDate measureDate;

    @ApiModelProperty(value = "度量周期(1-日,2-周,3-月,4-季度,5-年)")
    private Integer measureCycle;

    @ApiModelProperty(value = "度量周期名称")
    private String measureCycleName;

    @ApiModelProperty(value = "代码行数")
    private Long codeLines;

    @ApiModelProperty(value = "注释行数")
    private Long commentLines;

    @ApiModelProperty(value = "空行数")
    private Long blankLines;

    @ApiModelProperty(value = "总行数")
    private Long totalLines;

    @ApiModelProperty(value = "注释率(%)")
    private BigDecimal commentRate;

    @ApiModelProperty(value = "代码复杂度")
    private BigDecimal complexity;

    @ApiModelProperty(value = "代码重复率(%)")
    private BigDecimal duplicationRate;

    @ApiModelProperty(value = "技术债务(小时)")
    private BigDecimal technicalDebt;

    @ApiModelProperty(value = "可维护性评级(A-E)")
    private String maintainabilityRating;

    @ApiModelProperty(value = "可靠性评级(A-E)")
    private String reliabilityRating;

    @ApiModelProperty(value = "安全性评级(A-E)")
    private String securityRating;

    @ApiModelProperty(value = "单元测试覆盖率(%)")
    private BigDecimal testCoverage;

    @ApiModelProperty(value = "单元测试数量")
    private Integer testCount;

    @ApiModelProperty(value = "单元测试通过数量")
    private Integer testPassCount;

    @ApiModelProperty(value = "单元测试失败数量")
    private Integer testFailCount;

    @ApiModelProperty(value = "缺陷总数")
    private Integer bugCount;

    @ApiModelProperty(value = "严重缺陷数")
    private Integer criticalBugCount;

    @ApiModelProperty(value = "主要缺陷数")
    private Integer majorBugCount;

    @ApiModelProperty(value = "次要缺陷数")
    private Integer minorBugCount;

    @ApiModelProperty(value = "缺陷密度(缺陷数/千行代码)")
    private BigDecimal bugDensity;

    @ApiModelProperty(value = "代码审查覆盖率(%)")
    private BigDecimal reviewCoverage;

    @ApiModelProperty(value = "代码审查发现问题数")
    private Integer reviewIssueCount;

    @ApiModelProperty(value = "性能评分")
    private BigDecimal performanceScore;

    @ApiModelProperty(value = "响应时间(ms)")
    private BigDecimal responseTime;

    @ApiModelProperty(value = "吞吐量(TPS)")
    private BigDecimal throughput;

    @ApiModelProperty(value = "内存使用率(%)")
    private BigDecimal memoryUsage;

    @ApiModelProperty(value = "CPU使用率(%)")
    private BigDecimal cpuUsage;

    @ApiModelProperty(value = "质量门禁状态(1-通过,0-未通过)")
    private Integer qualityGateStatus;

    @ApiModelProperty(value = "质量门禁状态名称")
    private String qualityGateStatusName;

    @ApiModelProperty(value = "质量评分")
    private BigDecimal qualityScore;

    @ApiModelProperty(value = "度量工具")
    private String measureTool;

    @ApiModelProperty(value = "度量人员")
    private String measureBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

}
