package com.swcares.pt.quality.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.baseframe.common.core.dto.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.quality.dto.QualityProductMeasureDTO <br>
 * Description：产品质量度量 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@ApiModel(value="QualityProductMeasureDTO", description="产品质量度量")
public class QualityProductMeasureDTO extends BaseEntity implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @NotNull(message = "项目编码不能为空")
    @ExcelProperty("项目编码")
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @NotNull(message = "产品名称不能为空")
    @ExcelProperty("产品名称")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ExcelProperty("产品版本")
    @ApiModelProperty(value = "产品版本")
    private String productVersion;

    @NotNull(message = "度量日期不能为空")
    @ExcelProperty("度量日期")
    @ApiModelProperty(value = "度量日期")
    private LocalDate measureDate;

    @ExcelProperty("度量周期")
    @ApiModelProperty(value = "度量周期(1-日,2-周,3-月,4-季度,5-年)")
    private Integer measureCycle;

    @ExcelProperty("代码行数")
    @ApiModelProperty(value = "代码行数")
    private Long codeLines;

    @ExcelProperty("注释行数")
    @ApiModelProperty(value = "注释行数")
    private Long commentLines;

    @ExcelProperty("空行数")
    @ApiModelProperty(value = "空行数")
    private Long blankLines;

    @ExcelProperty("总行数")
    @ApiModelProperty(value = "总行数")
    private Long totalLines;

    @ExcelProperty("注释率(%)")
    @ApiModelProperty(value = "注释率(%)")
    private BigDecimal commentRate;

    @ExcelProperty("代码复杂度")
    @ApiModelProperty(value = "代码复杂度")
    private BigDecimal complexity;

    @ExcelProperty("代码重复率(%)")
    @ApiModelProperty(value = "代码重复率(%)")
    private BigDecimal duplicationRate;

    @ExcelProperty("技术债务(小时)")
    @ApiModelProperty(value = "技术债务(小时)")
    private BigDecimal technicalDebt;

    @ExcelProperty("可维护性评级")
    @ApiModelProperty(value = "可维护性评级(A-E)")
    private String maintainabilityRating;

    @ExcelProperty("可靠性评级")
    @ApiModelProperty(value = "可靠性评级(A-E)")
    private String reliabilityRating;

    @ExcelProperty("安全性评级")
    @ApiModelProperty(value = "安全性评级(A-E)")
    private String securityRating;

    @ExcelProperty("单元测试覆盖率(%)")
    @ApiModelProperty(value = "单元测试覆盖率(%)")
    private BigDecimal testCoverage;

    @ExcelProperty("单元测试数量")
    @ApiModelProperty(value = "单元测试数量")
    private Integer testCount;

    @ExcelProperty("单元测试通过数量")
    @ApiModelProperty(value = "单元测试通过数量")
    private Integer testPassCount;

    @ExcelProperty("单元测试失败数量")
    @ApiModelProperty(value = "单元测试失败数量")
    private Integer testFailCount;

    @ExcelProperty("缺陷总数")
    @ApiModelProperty(value = "缺陷总数")
    private Integer bugCount;

    @ExcelProperty("严重缺陷数")
    @ApiModelProperty(value = "严重缺陷数")
    private Integer criticalBugCount;

    @ExcelProperty("主要缺陷数")
    @ApiModelProperty(value = "主要缺陷数")
    private Integer majorBugCount;

    @ExcelProperty("次要缺陷数")
    @ApiModelProperty(value = "次要缺陷数")
    private Integer minorBugCount;

    @ExcelProperty("缺陷密度")
    @ApiModelProperty(value = "缺陷密度(缺陷数/千行代码)")
    private BigDecimal bugDensity;

    @ExcelProperty("代码审查覆盖率(%)")
    @ApiModelProperty(value = "代码审查覆盖率(%)")
    private BigDecimal reviewCoverage;

    @ExcelProperty("代码审查发现问题数")
    @ApiModelProperty(value = "代码审查发现问题数")
    private Integer reviewIssueCount;

    @ExcelProperty("性能评分")
    @ApiModelProperty(value = "性能评分")
    private BigDecimal performanceScore;

    @ExcelProperty("响应时间(ms)")
    @ApiModelProperty(value = "响应时间(ms)")
    private BigDecimal responseTime;

    @ExcelProperty("吞吐量(TPS)")
    @ApiModelProperty(value = "吞吐量(TPS)")
    private BigDecimal throughput;

    @ExcelProperty("内存使用率(%)")
    @ApiModelProperty(value = "内存使用率(%)")
    private BigDecimal memoryUsage;

    @ExcelProperty("CPU使用率(%)")
    @ApiModelProperty(value = "CPU使用率(%)")
    private BigDecimal cpuUsage;

    @ExcelProperty("质量门禁状态")
    @ApiModelProperty(value = "质量门禁状态(1-通过,0-未通过)")
    private Integer qualityGateStatus;

    @ExcelProperty("质量评分")
    @ApiModelProperty(value = "质量评分")
    private BigDecimal qualityScore;

    @ExcelProperty("度量工具")
    @ApiModelProperty(value = "度量工具")
    private String measureTool;

    @ExcelProperty("度量人员")
    @ApiModelProperty(value = "度量人员")
    private String measureBy;

    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注")
    private String remark;

}
