package com.swcares.pt.quality.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.pt.core.base.BaseDomain;
import com.swcares.pt.quality.repository.QualityProductMeasureRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.quality.domain.QualityProductMeasure <br>
 * Description：产品质量度量 领域模型 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityProductMeasure extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编码 */
    private String projectCode;

    /** 产品名称 */
    private String productName;

    /** 产品版本 */
    private String productVersion;

    /** 度量日期 */
    private LocalDate measureDate;

    /** 度量周期(1-日,2-周,3-月,4-季度,5-年) */
    private Integer measureCycle;

    /** 代码行数 */
    private Long codeLines;

    /** 注释行数 */
    private Long commentLines;

    /** 空行数 */
    private Long blankLines;

    /** 总行数 */
    private Long totalLines;

    /** 注释率(%) */
    private BigDecimal commentRate;

    /** 代码复杂度 */
    private BigDecimal complexity;

    /** 代码重复率(%) */
    private BigDecimal duplicationRate;

    /** 技术债务(小时) */
    private BigDecimal technicalDebt;

    /** 可维护性评级(A-E) */
    private String maintainabilityRating;

    /** 可靠性评级(A-E) */
    private String reliabilityRating;

    /** 安全性评级(A-E) */
    private String securityRating;

    /** 单元测试覆盖率(%) */
    private BigDecimal testCoverage;

    /** 单元测试数量 */
    private Integer testCount;

    /** 单元测试通过数量 */
    private Integer testPassCount;

    /** 单元测试失败数量 */
    private Integer testFailCount;

    /** 缺陷总数 */
    private Integer bugCount;

    /** 严重缺陷数 */
    private Integer criticalBugCount;

    /** 主要缺陷数 */
    private Integer majorBugCount;

    /** 次要缺陷数 */
    private Integer minorBugCount;

    /** 缺陷密度(缺陷数/千行代码) */
    private BigDecimal bugDensity;

    /** 代码审查覆盖率(%) */
    private BigDecimal reviewCoverage;

    /** 代码审查发现问题数 */
    private Integer reviewIssueCount;

    /** 性能评分 */
    private BigDecimal performanceScore;

    /** 响应时间(ms) */
    private BigDecimal responseTime;

    /** 吞吐量(TPS) */
    private BigDecimal throughput;

    /** 内存使用率(%) */
    private BigDecimal memoryUsage;

    /** CPU使用率(%) */
    private BigDecimal cpuUsage;

    /** 质量门禁状态(1-通过,0-未通过) */
    private Integer qualityGateStatus;

    /** 质量评分 */
    private BigDecimal qualityScore;

    /** 度量工具 */
    private String measureTool;

    /** 度量人员 */
    private String measureBy;

    /** 备注 */
    private String remark;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

    private QualityProductMeasureRepository repository;

    public QualityProductMeasure(QualityProductMeasureRepository repository) {
        this.repository = repository;
    }

    public static QualityProductMeasure build(QualityProductMeasureRepository repository) {
        return new QualityProductMeasure(repository);
    }

    /**
     * 保存质量度量记录
     * @return 是否保存成功
     */
    public boolean save() {
        // 业务逻辑验证
        validateQualityMeasure();
        // 计算衍生指标
        calculateDerivedMetrics();
        return this.repository.save(this);
    }

    /**
     * 验证质量度量数据
     */
    private void validateQualityMeasure() {
        if (this.projectCode == null || this.projectCode.trim().isEmpty()) {
            throw new IllegalArgumentException("项目编码不能为空");
        }
        if (this.productName == null || this.productName.trim().isEmpty()) {
            throw new IllegalArgumentException("产品名称不能为空");
        }
        if (this.measureDate == null) {
            throw new IllegalArgumentException("度量日期不能为空");
        }
    }

    /**
     * 计算衍生指标
     */
    private void calculateDerivedMetrics() {
        // 计算注释率
        if (this.codeLines != null && this.commentLines != null && this.codeLines > 0) {
            this.commentRate = BigDecimal.valueOf(this.commentLines)
                    .divide(BigDecimal.valueOf(this.codeLines), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }

        // 计算缺陷密度 (缺陷数/千行代码)
        if (this.bugCount != null && this.codeLines != null && this.codeLines > 0) {
            this.bugDensity = BigDecimal.valueOf(this.bugCount)
                    .divide(BigDecimal.valueOf(this.codeLines), 6, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(1000));
        }

        // 计算总行数
        if (this.codeLines != null && this.commentLines != null && this.blankLines != null) {
            this.totalLines = this.codeLines + this.commentLines + this.blankLines;
        }

        // 计算单元测试失败数量
        if (this.testCount != null && this.testPassCount != null) {
            this.testFailCount = this.testCount - this.testPassCount;
        }
    }

}
