package com.swcares.pt.quality.dto;

import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO <br>
 * Description：产品质量度量 分页查询对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-12-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="QualityProductMeasurePagedDTO", description="产品质量度量分页查询")
public class QualityProductMeasurePagedDTO extends PagedDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品版本")
    private String productVersion;

    @ApiModelProperty(value = "度量周期(1-日,2-周,3-月,4-季度,5-年)")
    private Integer measureCycle;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "质量门禁状态(1-通过,0-未通过)")
    private Integer qualityGateStatus;

    @ApiModelProperty(value = "可维护性评级(A-E)")
    private String maintainabilityRating;

    @ApiModelProperty(value = "可靠性评级(A-E)")
    private String reliabilityRating;

    @ApiModelProperty(value = "安全性评级(A-E)")
    private String securityRating;

    @ApiModelProperty(value = "度量工具")
    private String measureTool;

    @ApiModelProperty(value = "度量人员")
    private String measureBy;

}
